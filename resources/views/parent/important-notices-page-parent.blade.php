@php
    $showCaptcha = false;
    if (request()->query('showCaptcha') === '1') {
        $showCaptcha = true;
    }
@endphp

@extends('layouts.default-page', ['$pageTitle' => $pageTitle, 'is_homepage' => false])

@section('page_id', 'important-notices')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/important-notices-page/important-notices.css') }}">
    <link rel="stylesheet" href="{{ asset('css/component/input/input.css') }}">
@endpush

@section('content')
    <section class="gld-pages-banner-container" id="important-notices-banner"></section>
    @include('include.breadcrumb', [
        'homepage' => true,
        'pageTitle' => $pageTitle
    ])

    <livewire:notice.important-notice showCaptcha="{{ $showCaptcha }}" />
@endsection

@push('scripts')
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
@endpush
