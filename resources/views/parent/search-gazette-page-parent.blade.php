@php
    // Perform your PHP checks here
    if (!session('confirmed_view_gazette')) {
        $locale = app()->getLocale();
        // Redirect to important notices if the condition is met
        header('Location: ' . route('important-notices', ['locale' => $locale]) . '?showCaptcha=1');
        exit;
    }
@endphp

@extends('layouts.default-page', ['$pageTitle' => __('general.title.' . $pageTitle), 'is_homepage' => false])

@section('page_id', 'search-gazette')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/component/input/input.css') }}">
    <link rel="stylesheet" href="{{ asset('css/search-gazette-page/search-gazette.css') }}">
@endpush

@section('content')
    <section class="gld-pages-banner-container" id="search-gazette-banner"></section>
    @include('include.breadcrumb', [
        'parent.homepage' => false,
        'pageTitle' =>$pageTitle,
    ])
    <livewire:search.search-gazette-page/>
@endsection
