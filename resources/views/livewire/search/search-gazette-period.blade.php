<fieldset class="search-gazette-period-container">
    <legend class="sr-only">Select Period</legend>
    <!-- <div class="label1 search-gazette-period-button">
        <input type="radio" name="period" id="2024-2020" value="2024-2020" wire:model.live="$parent.period" checked/>
        <label for="2024-2020">2024-2020</label>
    </div>
    <div class="label1 search-gazette-period-button">
        <input type="radio" name="period" id="2019-2015" value="2019-2015" wire:model.live="$parent.period"/>
        <label for="2019-2015">2019-2015</label>
    </div>
    <div class="label1 search-gazette-period-button">
        <input type="radio" name="period" id="2014-2010" value="2014-2010" wire:model.live="$parent.period"/>
        <label for="2014-2010">2014-2010</label>
    </div>
    <div class="label1 search-gazette-period-button">
        <input type="radio" name="period" id="2009" value="2009" wire:model.live="$parent.period"/>
        <label for="2009">2009 and Ago</label>
    </div> -->
    @php
        $count = 0;
    @endphp
    @foreach ($displayPeriod as $period)
        <div class="label1 search-gazette-period-button">
            @if ($count == 0)
                <input type="radio" name="period" id="{{ $period }}" value="{{ $period }}" wire:model.live="$parent.period" checked/>
            @else
                <input type="radio" name="period" id="{{ $period }}" value="{{ $period }}" wire:model.live="$parent.period"/>
            @endif
            <label for="{{ $period }}">{{ $period }}</label>
        </div>
        @php
            $count++;
        @endphp
    @endforeach
</fieldset>
