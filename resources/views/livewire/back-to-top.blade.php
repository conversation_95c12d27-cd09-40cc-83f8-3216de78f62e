<a type="button" id="back-to-top-button" href="#main">
    <span class="gld-svg-container">
        <span class="arrow-up-icon svg"></span>
    </span>
    <span class="back-to-top-text label2">{{ __('general.back_to_top') }}</span>
</a>

@script
    <script>
        window.addEventListener('scroll', function() {
            var footer = document.querySelector('footer');
            var scrollY = window.scrollY;
            var footerTop = footer.getBoundingClientRect().top + scrollY;
            var windowHeight = window.innerHeight;

            if (scrollY + windowHeight >= footerTop) {
                $wire.$el.classList.add('fixed');
            } else {
                $wire.$el.classList.remove('fixed');
            }

            if (scrollY > 100) {
                $wire.$el.classList.add('active');
            } else {
                $wire.$el.classList.remove('active');
            }
        });


    </script>
@endscript
