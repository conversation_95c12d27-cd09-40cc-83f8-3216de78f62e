<div class="table-pagination">
    <div class="left">
        <livewire:input.select.default-select
            name="perPage"
            id="select-table-row-options"
            aria_label="{{__('general.pagination.select_row_per_page')}}"
            :type="['button' => 'white', 'options' => 'default']"
            :selected="$paginationData['per_page']"
            :disabled="false"
            :options="[
                      '5' => '5',
                      '20' => '20',
                      '50' => '50']"
            updateFn="update-property"
        />
        <div class="label2 table-per-page">
            {{__('general.pagination.per_page', [
                'current' => ($paginationData['current_page'] - 1) * $paginationData['per_page'] + 1,
                'current_total' => min($paginationData['current_page'] * $paginationData['per_page'], $paginationData['total']),
                'total' => $paginationData['total']
            ])}}
{{--            Show {{ ($paginationData['current_page'] - 1) * $paginationData['per_page'] + 1 }}-{{ min($paginationData['current_page'] * $paginationData['per_page'], $paginationData['total']) }} of {{ $paginationData['total'] }}--}}
        </div>
    </div>
    <div class="right">
        <button class="table-prev-btn" wire:click="previousPage" title="{{__('general.pagination.previous')}}" aria-label="{{__('general.pagination.previous')}}" @if($paginationData['current_page'] <= 1 || $prevDisabled) disabled @endif>
            <span class="gld-svg-container">
                <span title="Previous page" class="prev-btn-icon svg"></span>
            </span>
        </button>
        <div class="table-select-page label2">
            {{__('general.pagination.page')}}
            <input type="text" id="currentPage" name="currentPage" value="{{ $currentPage }}" aria-label="{{__('general.pagination.go_to_page')}}" class="number-input label2" wire:model.lazy="currentPage" oninput="this.value = this.value.replace(/[^0-9]/g, '');" />
            {{__('general.pagination.of')}} {{ $paginationData['last_page'] }}
        </div>
        <button class="table-next-btn" wire:click="nextPage" title="{{__('general.pagination.next')}}" aria-label="{{__('general.pagination.next')}}" @if($paginationData['current_page'] >= $paginationData['last_page'] || $nextDisabled) disabled @endif>
            <span class="gld-svg-container">
                <span title="Next page" class="next-btn-icon svg"></span>
            </span>
        </button>
    </div>
</div>
@script
<script>
    $wire.$watch('page', (value) => {
        $wire.$set('currentPage', value);
    });

    $wire.$watch('paginationData', (value) => {
        console.log(value);
        $wire.$set('totalPage', Math.ceil(value.total / value.per_page));
    });

    $wire.$watch('paginationData.per_page', (value) => {
        // Scroll to the top of the table
        const table = document.getElementById('search-results-table');
        if (table) {
          table.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
</script>
@endscript
