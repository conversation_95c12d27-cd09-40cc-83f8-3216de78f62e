<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;

class CreateAnonymousSessionController extends Controller
{
    public function createAnonymousSession(Request $request)
    {
        $autoPass = config('turnstile.autoPass');
        if ($autoPass) {
            // Create an anonymous session without Turnstile verification
            Session::put('anonymous', true);
            Session::put('confirmed_view_gazette', true);

            return response()->json(['success' => true]);
        }

        $token = $request->input('token');

        // Verify the Turnstile token with Cloudflare (you need to implement this part)
        $isValid = $this->verifyTurnstileToken($token);

        if ($isValid) {
            // Create an anonymous session
            Session::put('anonymous', true);
            Session::put('confirmed_view_gazette', true);

            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 400);
        }
    }

    private function verifyTurnstileToken($token)
    {
        $response = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
            'secret' => config('app.turnstile_secret_key'),
            'response' => $token,
        ]);

        return $response->json()['success'];
    }

    public function createAnonymousSessionLocal(Request $request)
    {
        // Create an anonymous session
        Session::put('anonymous', true);
        Session::put('confirmed_view_gazette', true);

        return response()->json(['success' => true]);
    }
}
