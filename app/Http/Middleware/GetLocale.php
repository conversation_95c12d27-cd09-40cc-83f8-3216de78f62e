<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
class GetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->route('locale');

        if ($locale) {
            if (in_array($locale, ['en', 'zh'])) {
                app()->setLocale($locale);
            }
        } else {
            if ($request->session()->has('locale')){
                app()->setLocale($request->session()->get('locale'));
            }
        }

        return $next($request);
    }
}
