<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use AWS\S3\S3Client;

class DownloadFileFromS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:download-file-from-s3 {filename}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'parameter filename to download from S3 bucket';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        // $filename = '2009.tar';
        $filename = $this->argument('filename');
        $localFilePath = 'files/' . $filename;

        if (Storage::disk('object_storage')->exists($filename)) {
            // $fileSize = Storage::disk('object_storage')->size($filename);
            // \Log::info("Downloading file: {$filename}, Size: " . ($fileSize / 1024 / 1024) . " MB");

            // $readStream = Storage::disk('object_storage')->readStream($filename);
            // Storage::disk('local')->writeStream($localFilePath, $readStream);

            // if (is_resource($readStream)) {
            //     fclose($readStream);
            // }

            // print "ok\n";
            // print config('filesystems.disks.object_storage.key') . "\n"; die();

            // Initialize S3 client
            $s3Client = new S3Client([
                'version' => 'latest',
                'region' => config('filesystems.disks.object_storage.region', 'us-east-1'),
                'endpoint' => config('filesystems.disks.object_storage.endpoint'),
                'use_path_style_endpoint' => config('filesystems.disks.object_storage.use_path_style_endpoint', false),
                'credentials' => [
                    'key' => config('filesystems.disks.object_storage.key'),
                    'secret' => config('filesystems.disks.object_storage.secret'),
                ],
            ]);

            // Verify S3 file exists
            $bucket = config('filesystems.disks.object_storage.bucket');
            $head = $s3Client->headObject([
                'Bucket' => $bucket,
                'Key' => $filename,
            ]);
            $fileSize = $head['ContentLength'];
            \Log::info("Downloading file: {$filename}, Size: " . ($fileSize / 1024 / 1024) . " MB");

            // Download with streaming
            $writeStream = fopen(storage_path('app/private/' . $localFilePath), 'wb');
            if (!$writeStream) {
                \Log::error("Failed to open local write stream for: {$localFilePath}");
                throw new \Exception("Failed to open local write stream");
            }

            $s3Client->getObject([
                'Bucket' => $bucket,
                'Key' => $filename,
                '@http' => ['sink' => $writeStream], // Stream directly to file
            ]);

            // if (is_resource($writeStream)) {
            //     fclose($writeStream);
            // } else {
            //     \Log::error("Failed to close local write stream for: {$localFilePath}");
            //     throw new \Exception("Failed to close local write stream");
            // }

            // Verify file size
            // $localSize = filesize(storage_path('app/private' . $localFilePath));
            // if ($localSize !== $fileSize) {
            //     \Log::error("Size mismatch: S3={$fileSize}, Local={$localSize} for {$localFilePath}");
            //     throw new \Exception("Downloaded file size mismatch");
            // }

            \Log::info("Successfully downloaded: {$filename} to {$localFilePath}");
            print "ok\n";
        } else {
            return "File not found.";
        }
    }
}
