<?php

namespace App\Livewire\TextSize;

use App\Models\WebsiteContent;
use Livewire\Component;

class TextSizePage extends Component
{
    public array $data = [];

    private function fetchStyledContent(): void
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'TextSize')
            ->get()
            ->pluck($content, 'key');

        $classes = [
            '<h3>' => '<h3 class="h3 gld-card-article-section-title">',
            '<h4>' => '<div class="text-size-subtitle">',
            '</h4>' => '</div>',
            '<p>'  => '<p class="gld-card-article-section-body body1">',
            '<ul>' => '<ul class="gld-card-article-section-body body1">',
            '<em>' =>  '<em class="gld-card-article-section-body body1 italic" >',
        ];

        if ($contents->isNotEmpty()) {
            foreach ($contents as $key => $item) {
              if (preg_match('/section_(\d+)_/', $key, $matches)) {
                  $sectionNumber = $matches[1];
                  $this->data[$sectionNumber][$key] = addClasses(html_entity_decode($contents[$key], ENT_QUOTES, "UTF-8"), $classes);
              }
            }
        }

    }


    public function render()
    {
        $this->fetchStyledContent();
        return view('livewire.text-size.text-size-page');
    }
}
