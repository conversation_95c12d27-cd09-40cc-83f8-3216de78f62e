<?php

namespace App\Livewire\Input\Select;

use Livewire\Attributes\Modelable;
use Livewire\Attributes\Reactive;
use Livewire\Attributes\On;
use Livewire\Component;

class DefaultSelect extends Component
{
    public bool $open = false;

    public string $customRender = '';
    public string $showHideContent = '';
    public string $aria_label = '';
    public string $id = '';
    public string $label = '';
    public string $name = '';
    public string $updateFn = '';
    public array $type = [];
    #[Reactive] public $selected;
    #[Reactive] public bool $disabled = true;
    #[Reactive] public array $options = [];
    public string $firstOption = '';

    public function mount($options, $disabled, $selected, $type, $showHideContent = ''): void
    {
        $this->showHideContent = $showHideContent;
        $this->disabled = $disabled;
        $this->options = $options;
        $this->selected = $selected;
        $this->type = $type;
        $this->firstOption = reset($options);
    }

    public function closeMenu()
    {
        $this->open = false;
    }

    public function toggleCollapsed(): void
    {
        $this->open = !$this->open;
        $this->dispatch('toggleCollapsed');
    }

    public function selectOption($key): void
    {
        $this->open = false;
        if ($this->updateFn) {
            $this->dispatch($this->updateFn, propertyName: $this->name, value: $key );
        }
    }

    public function render()
    {
        return view('livewire.input.select.default-select');
    }
}
