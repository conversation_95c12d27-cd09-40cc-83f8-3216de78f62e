<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;

class Header extends Component
{
    public bool $open = false;
    public bool $showShare = false;
    public bool $headerActive = false;
    public string $locale = 'en';
    public string $activeRoute = '';
    public string $shareUrl = '';
    public string $pageTitle = '';
    public array $shareContent = [];

    protected $listeners = ['toggleHeaderActive'];

    public function mount(string $pageTitle): void
    {
        $this->locale = app()->getLocale();
        $this->currentUrl = url()->current();
        $this->setActiveItem();
        $this->shareUrl = rawurlencode("\n" . Request::fullUrl());
        $this->pageTitle = $pageTitle
            ? "The Government of the Hong Kong Special Administrative Region Gazette - " . rawurlencode($pageTitle)
            : "The Government of the Hong Kong Special Administrative Region Gazette";
        $this->shareContent = [
            "url" => $this->shareUrl,
            "title" => $this->pageTitle,
            "body" => $this->pageTitle . $this->shareUrl
        ];
    }

    public function toggleHeaderActive($status): void
    {
        $this->headerActive = $status;
    }

    public function toggleShare(): void
    {
        $this->showShare = !$this->showShare;
    }

    public function printPage(): void
    {
        $this->dispatch('printPage');
    }

    public function openSharePopup(string $url): void
    {
        $this->dispatch('openSharePopup', $url);
    }

    public function shareByEmail(): void
    {
        $mailto = "mailto:?subject=" . $this->shareContent['title'] . "&body=" . $this->shareContent['body'];
        $this->dispatch('openMailto', $mailto);
    }

    public function setActiveItem()
    {
        $this->activeRoute = Request::route()->getName();
    }


    public function render()
    {
        return view('livewire.header');
    }
}
