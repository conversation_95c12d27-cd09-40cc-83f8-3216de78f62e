<?php

namespace App\Livewire\ListOfGazette;

use Livewire\Component;
use Livewire\Attributes\Url;
use App\Models\Gazette;

class ListOfGazetteResult extends Component
{
    public bool $pagination = false;
    public bool $expandable = true;
    public string $title = 'table title';
    public $page = 1;
    public int $gid = 1;
    public string $category = '';
    public array $categoryOptions = ['mg', 'ls1', 'ls2', 'ls3', 'ls4', 'ls5', 'ls6'];

    public $gazette = null;
    public $notices = [];

    public $column = [];
    public $tableName = '';

    public $ls4Parent = null;

    public function mount()
    {
        $this->gid = request()->route('id', 1);
        $this->category = request()->route('category', '');
        $this->ls4Parent = request()->get('parent', null);
        if (!in_array($this->category, $this->categoryOptions)) {
            $this->category = $this->categoryOptions[0];
        }

        $this->column = [2,3,4,5,6,7]; // 2:date 3:num 4:ls 5:iny 6:io 7:in

        $this->gazette = Gazette::with('mg', 'mg.typeDetail', 'ls1', 'ls1.typeDetail', 'ls2', 'ls2.typeDetail', 'ls3', 'ls3.typeDetail', 'ls5', 'ls5.typeDetail')->findOrFail($this->gid);
        if ($this->gazette) {
            $this->gazette->ls4 = $this->gazette->ls4($this->ls4Parent);
            $this->gazette->ls6 = $this->gazette->ls6();

            $tempNotices = [];
            switch ($this->category) {
                case 'mg':
                    $this->column = [2,3];
                    $this->tableName = 'mg';
                    if ($this->gazette->mg) {
                        foreach ($this->gazette->mg as $notice) {
                            // \Log::info($notice->type);
                            $fileNames = $this->generatePdfLinks($this->gazette, 'mg', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
                case 'ls1':
                    $this->column = [2,7,6];
                    $this->tableName = 'ls1';
                    if ($this->gazette->ls1) {
                        foreach ($this->gazette->ls1 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls1', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->year = $this->gazette->year;
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
                case 'ls2':
                    $this->column = [2,7,6];
                    $this->tableName = 'ls2';
                    if ($this->gazette->ls2) {
                        foreach ($this->gazette->ls2 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls2', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->year = $this->gazette->year;
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
                case 'ls3':
                    $this->column = [2,7,6];
                    $this->tableName = 'ls3';
                    if ($this->gazette->ls3) {
                        foreach ($this->gazette->ls3 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls3', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->year = $this->gazette->year;
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
                case 'ls4':
                    if ($this->gazette->ls4) {
                        $this->tableName = 'ls4';
                        foreach ($this->gazette->ls4 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls4', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->e_title = $notice->e_level_title;
                            $notice->c_title = $notice->c_level_title;
                            $tempNotices[] = $notice;
                        }
                    }
                    $this->notices = $tempNotices;
                    break;
                case 'ls5':
                    $this->column = [2,7,6];
                    $this->tableName = 'ls5';
                    if ($this->gazette->ls5) {
                        foreach ($this->gazette->ls5 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls5', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->year = $this->gazette->year;
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
                case 'ls6':
                    $this->column = [];
                    $this->tableName = 'ls6';
                    if ($this->gazette->ls6) {
                        foreach ($this->gazette->ls6 as $notice) {
                            $fileNames = $this->generatePdfLinks($this->gazette, 'ls6', $notice);
                            $notice->englishPdfUrl = $fileNames['englishPdfUrl'];
                            $notice->chinesePdfUrl = $fileNames['chinesePdfUrl'];
                            $notice->year = $this->gazette->year;
                            $notice->e_title = $notice->group;
                            $tempNotices[] = $notice;
                        }
                    }

                    // group the notices by typeDetail
                    $tempNoticesArray = collect($tempNotices)->groupBy('typeDetail')->toArray();
                    $tempNotices = array_map(function($notices) {
                        return $notices;
                    }, $tempNoticesArray);

                    $this->notices = $tempNotices;
                    break;
            }

            if ($this->tableName != 'ls4') {
                $tempNotices = [];
                foreach ($this->notices as $noticeType => $notices) {
                    $tempNotices[$noticeType] = [];
                    foreach ($notices as $tempNotice) {
                        $tempNotice = (object)$tempNotice;
                        $tempNotice->table_name = $this->tableName;
                        $tempNotice->dayDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('d');
                        $tempNotice->monthDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('m');
                        $tempNotice->yearDate = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('Y');
                        $tempNotice->enDayOfWeek = \DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('l');
                        $tempNotice->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $tempNotice->dateno)->format('w'));
                        $tempNotice->noticeType = $noticeType;

                        $tempNotices[$noticeType][] = $tempNotice;
                    }
                }
            } else {
                // $tempNotices = [];
                // foreach ($this->notices as $notice) {
                //     $notice = (object)$notice;
                //     $notice->table_name = $this->tableName;
                //     $notice->dayDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('d');
                //     $notice->monthDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('m');
                //     $notice->yearDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('Y');
                //     $notice->enDayOfWeek = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('l');
                //     $notice->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $notice->dateno)->format('w'));
                //     $notice->noticeType = 'ls4';

                //     $tempNotices[0][] = $notice;
                // }
                // \Log::info($tempNotices);

                $tempNotices = [];
                foreach ($this->notices as $notice) {
                    $notice = (object)$notice;
                    $notice->table_name = $this->tableName;
                    $notice->dayDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('d');
                    $notice->monthDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('m');
                    $notice->yearDate = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('Y');
                    $notice->enDayOfWeek = \DateTime::createFromFormat('Ymd', $notice->dateno)->format('l');
                    $notice->zhDayOfWeek = $this->getDayOfWeek(\DateTime::createFromFormat('Ymd', $notice->dateno)->format('w'));
                    $notice->noticeType = 'ls4';

                    $tempNotices[] = $notice;
                }
            }

            $this->notices = $tempNotices;
        }
    }

    public function render()
    {
        return view('livewire.list-of-gazette.list-of-gazette-result');
    }

    public function generatePdfLinks($gazette, $type, $notice)
    {
        $result = [];

        switch ($type) {
            case 'mg':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'egn', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $gazette->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'cgn', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $gazette->extra]),
                ];
                break;
            case 'ls1':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'es1', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'cs1', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                ];
                break;
            case 'ls2':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'es2', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'cs2', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                ];
                break;
            case 'ls3':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'es3', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'cs3', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                ];
                break;
            case 'ls4':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'ls4', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => '1', 'extra' => '1', 'nid' => $notice->id, 'language' => 'en']),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'ls4', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => '1', 'extra' => '1', 'nid' => $notice->id, 'language' => 'zh']),
                ];
                break;
            case 'ls5':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 'es5', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 'cs5', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'notice_no' => $notice->notice_no, 'extra' => $notice->extra]),
                ];
                break;
            case 'ls6':
                $result = [
                    'englishPdfUrl' => route('pdf.show', ['type' => 's6', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'group' => 'p'.$notice->group, 'extra' => $notice->extra]),
                    'chinesePdfUrl' => route('pdf.show', ['type' => 's6', 'year' => $gazette->year, 'volume' => $this->correctVolume($gazette->volume), 'gno' => $this->correctGno($gazette->gno), 'group' => 'p'.$notice->group, 'extra' => $notice->extra]),
                ];
                break;
        }

        return $result;
    }

    private function correctVolume($volume)
    {
        if ($volume < 10) {
            return '0'.$volume;
        }
        return $volume;
    }

    private function correctGno($gno)
    {
        if ($gno < 10) {
            return '0'.$gno;
        }
        return $gno;
    }

    private function getDayOfWeek($weekday) {
        $weekdays_text = array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        return $weekdays_text[$weekday];
    }
}
