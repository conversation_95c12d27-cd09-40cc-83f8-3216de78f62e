<?php

namespace App\Livewire\ContactUs;

use App\Models\WebsiteContent;
use Livewire\Component;

class ContactUsPage extends Component
{
    public array $data = [];
    public array $keys = [

        'general_title',
        'general_email',
        'general_fax',
        'general_address',
        'general_gazette_tel',
        'general_tech_support_tel',
        'ls6_title',
        'ls6_email',
        'ls6_fax',
        'ls6_address',
        'ls6_tel',
        'header',
        'footer',
        'ls6_more_title',
        'ls6_more_url',
    ];

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'Contact')
            ->whereIn('key', $this->keys)
            ->get()
            ->pluck($content, 'key');

        $classes = [
            '<p>' => '',
            '</p>' => '',
        ];

        foreach ($this->keys as $key) {
            $this->data[$key] = addClasses($contents[$key], $classes);
        }

        return view('livewire.contact-us.contact-us-page');
    }
}
