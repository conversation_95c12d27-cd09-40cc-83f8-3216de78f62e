<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Gazette;
use App\Models\Mg;
use App\Models\Ls1;
use App\Models\Ls2;
use App\Models\Ls3;
use App\Models\Ls4;
use App\Models\Ls5;
use App\Models\Ls6;
use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use DirectoryIterator;
use Spatie\PdfToText\Pdf;

class IndexNoticesByYear extends Component
{
    public $year = '';
    public $gno = '';

    public function render()
    {
        // $this->insertNoticeByLs6File();
        // return view('livewire.index-notices-by-year')->layout('layouts.publication-page');
        return view('livewire.index-notices-by-year')->layout('layouts.app');
    }

    public function indexNoticesByYear()
    {
        set_time_limit(0);
        $this->validate([
            'year' => 'required|numeric',
            //'gno' => 'required|numeric',
        ]);

      // $allYearGazette = Gazette::where('year', $this->year)->where('gno', $this->gno)->get();
      $allYearGazette = Gazette::where('year', $this->year)->get();
      $documents = [];

      \Log::info("Start at: " . date('Y-m-d H:i:s'));
      foreach ($allYearGazette as $gazette) {
          $documents = [];
          $mg = Mg::where('gazette_id', $gazette->id)->get();  
          $ls1 = Ls1::where('gazette_id', $gazette->id)->get();
          $ls2 = Ls2::where('gazette_id', $gazette->id)->get();
          $ls3 = Ls3::where('gazette_id', $gazette->id)->get();
          $ls4 = Ls4::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();
          $ls5 = Ls5::where('gazette_id', $gazette->id)->get();
          $ls6 = Ls6::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();

          foreach ($mg as $notice) {
              $fileNames = $this->getFileNames('mg', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'mg', $notice, $notice->e_title, 'en');
              $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'mg', $notice, $notice->c_title, 'zh');
          }

          foreach ($ls1 as $notice) {
              $fileNames = $this->getFileNames('ls1', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls1', $notice, $notice->e_title, 'en');
              $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'ls1', $notice, $notice->c_title, 'zh');
          }

          foreach ($ls2 as $notice) {
              $fileNames = $this->getFileNames('ls2', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls2', $notice, $notice->e_title, 'en');
              $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'ls2', $notice, $notice->c_title, 'zh');
          }

          foreach ($ls3 as $notice) {
              $fileNames = $this->getFileNames('ls3', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls3', $notice, $notice->e_title, 'en');
              $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'ls3', $notice, $notice->c_title, 'zh');
          }

          foreach ($ls4 as $notice) {
              $fileNames = $this->getFileNames('ls4', $gazette, $notice);
              if (!empty($fileNames['eFileName'])) $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls4', $notice, $notice->e_level_title, 'en');
              if (!empty($fileNames['cFileName'])) $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'ls4', $notice, $notice->c_level_title, 'zh');
          }

          foreach ($ls5 as $notice) {
              $fileNames = $this->getFileNames('ls5', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls5', $notice, $notice->e_title, 'en');
              $documents[] = $this->prepareDocument($fileNames['cFileName'], $gazette, 'ls5', $notice, $notice->c_title, 'zh');
          }

          foreach ($ls6 as $notice) {
              \Log::info("notice id: " . $notice->id);
              $fileNames = $this->getFileNames('ls6', $gazette, $notice);
              $documents[] = $this->prepareDocument($fileNames['eFileName'], $gazette, 'ls6', $notice, $notice->e_title, 'en');
          }

          \Log::info($gazette->dateno);
          \Log::info($gazette->year.$gazette->volume.$gazette->gno);
          \Log::info(count($documents));

          $this->indexDocuments($documents);
      }

      \Log::info("END at: " . date('Y-m-d H:i:s'));
    }

    private function getFileNames($type, $gazette, $notice)
    {
        $fileNames = '';
        $eFileName = '';
        $cFileName = '';
        $gno = $gazette->gno;
        if ($gno < 10) $gno = "0".$gazette->gno;
        $volume = $gazette->volume;
        if ($volume < 10) $volume = "0".$gazette->volume;
        switch ($type) {
            case 'mg':
                $eFileName = 'egn'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cgn'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls1':
                $eFileName = 'es1'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs1'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls2':
                $eFileName = 'es2'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs2'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls3':
                $eFileName = 'es3'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs3'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls4':
                $eFileName = $notice->e_filename;
                $cFileName = $notice->c_filename;
                break;
            case 'ls5':
                $eFileName = 'es5'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs5'.$gazette->year.$volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls6':
                $eFileName = 's6'.$gazette->year.$volume.$gno.'p'.$notice->group.'.pdf';
                break;
        }

        $fileNames = [
            'eFileName' => $eFileName,
            'cFileName' => $cFileName,
        ];

        return $fileNames;

    }

    private function prepareDocument($filename, $gazette, $notice_type, $notice, $title, $language)
    {
        $gno = $gazette->gno;
        if ($gno < 10) $gno = "0".$gazette->gno;
        $extra = "";
        if ($gazette->extra == "1") $extra = "e";
        $volume = $gazette->volume;
        if ($volume < 10) $volume = "0".$gazette->volume;
        // $filePath = storage_path('app/public/egazette/' . $gazette->year.$gazette->volume.$gno.$extra . '/' . $filename);
        $filePath = storage_path('app/private/files/' . $gazette->year.$volume.$gno.$extra . '/' . $filename);
        \Log::info("filePath: " . $filePath);
        \Log::info("filename; " . $filename);
        \Log::info("notice type: " . $notice_type);
        \Log::info("gazette id: " . $gazette->id);
        if (file_exists($filePath)) {
            // $fileContent = file_get_contents($filePath);
            // $fileContentBase64 = base64_encode($fileContent);
            //$fileContent = (new Pdf())
            //  ->setPdf($filePath)
            //  ->setOptions(['opw', $this->password])->text();
            try {
                $fileContent = (new Pdf())
                ->setPdf($filePath)->text();


                $result = [
                    'index' => 'gazette',
                    'body' => [
                            'attachment' => [
                            'content' => $fileContent
                        ],
                        'filename' => $filename,
                        'title' => $title,
                        'language' => $language,
                        'extra' => $gazette->extra,
                        'gazette_id' => $gazette->id,
                        'dateno' => $gazette->dateno,
                        'volume' => $gazette->volume,
                        'gno' => $gazette->gno,
                        'notice_no' => $notice->notice_no ?? '',
                        'notice_type' => $notice_type,
                        'notice_id' => $notice->id,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                ];
                // \Log::info($result);
                return $result;
            } catch (\Exception $e) {
                \Log::error("Error processing file: " . $filename);
                \Log::error($e->getMessage());

                return [];
            }
        }

        return null;
    }

    private function indexDocuments($documents)
    {
        \Log::info("indexDocuments: " . count($documents));
        // \Log::info("documents: ", $documents); die();
        $client = ClientBuilder::create()
            // ->setHosts(['http://***************:9200'])
            // ->setBasicAuthentication('elastic', 'demo1234')
            ->setHosts([config('elasticsearch.ES_HOSTS')])
            ->setBasicAuthentication(config('elasticsearch.ES_USERNAME'), config('elasticsearch.ES_PASSWORD'))
            ->setSSLVerification(config('elasticsearch.ES_SSL_CERT'))
            ->build();

        // index the document one by one
        foreach ($documents as $document) {
            \Log::info("indexDocuments: ");
            \Log::info($document);
            if ($document && count($document) > 0 && (!in_array($document['body']['filename'], ['cgn201923345217.pdf', 'egn201923345217.pdf', 'cgn201721254091.pdf', 'egn201721254091.pdf']))) {
                $fileContentBase64 = $document['body']['attachment']['content'];

                // split the base64 encoded file content into chunks to avoid the error "Request Entity Too Large"
                $chunkSize = 5 * 1024 * 1024; // 5MB
                $chunks = str_split($fileContentBase64, $chunkSize);
                foreach ($chunks as $index => $chunk) {
                    $document['data'] = $chunk;
                    \Log::info("indexing chunk: " . $document['body']['filename'] . " chunk: " . ($index + 1));
                    $this->indexDocumentChunk($client, $document);
                }
            }
        }
    }

    private function indexDocumentChunk($client, $params, $retries = 3)
    {
        \Log::info("indexDocumentChunk: " . $params['body']['filename']);
        $attempt = 0;
        $delay = 0; // Initial delay in seconds

        while ($attempt < $retries) {
            try {
                \Log::info("indexing document: " . $params['body']['filename']);
                $response = $client->index($params);
                \Log::info($response);
                sleep($delay);
                return;
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
                //Log::error("retryBulkIndex params: ");
                //Log::error($params);
                $attempt++;
                //sleep($delay);
                $delay *= 2; // Exponential backoff
            }
        }

        \Log::error('Failed to index documents after ' . $retries . ' attempts.');
        return;
    }

    public function insertNoticeByLs6File()
    {
        // $baseFolderPath = storage_path('app/public/egazette/');
        $baseFolderPath = storage_path('app/private/files/');
        $baseDirectory = new DirectoryIterator($baseFolderPath);

        foreach ($baseDirectory as $folder) {
            if ($folder->isDir() && !$folder->isDot()) {
                $folderName = $folder->getFilename();
                $folderPath = $baseFolderPath . $folderName;
                $directory = new DirectoryIterator($folderPath);

                foreach ($directory as $file) {
                    if ($file->isFile() && $file->getExtension() === 'pdf' && strpos($file->getFilename(), 's6') === 0) {
                        // Get the filename
                        $filename = $file->getFilename();

                        // Extract information from the filename 's6YYYYVVGGpG.pdf'
                        $year = (int) substr($filename, 2, 4);
                        $volume = (int) substr($filename, 6, 2);
                        $gno = (int) substr($filename, 8, 2);
                        $group = (int) substr($filename, 11, 1);

                        $gazette = Gazette::where('volume', $volume)->where('gno', $gno)->where('year', $year)->where('extra', '0')->first();
                        if ($gazette == null) {
                          \Log::info($filename);
                          continue;
                        }

                        // Insert a record into the database
                        DB::table('ls6')->insert([
                            'dateno' => $gazette->dateno,
                            'volume' => $volume,
                            'gno' => $gno,
                            'group' => $group,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                }
            }
        }
    }
}
