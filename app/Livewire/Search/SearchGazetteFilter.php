<?php

namespace App\Livewire\Search;

use App\Models\SearchGazettePeriod as SearchGazettePeriodModel;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\Reactive;
use Livewire\Component;

class SearchGazetteFilter extends Component
{

    public bool $isShowFilter = true;
    public string $period = '';
    #[Modelable] public array $category = [];
    #[Reactive] public string $yearAndVolume = '';
    #[Reactive] public string $searchKeyword = '';
    #[Reactive] public string $dateAndIssue = '';
    #[Reactive] public string $supplementNo = '';
    #[Reactive] public string $group = '';
    #[Reactive] public array $yearAndVolumeOptions = [];
    #[Reactive] public array $dateAndIssueOptions = [];
    #[Reactive] public array $categoriesResultCount = [];

    public function mount($period, $searchKeyword, $yearAndVolume, $yearAndVolumeOptions, $dateAndIssue, $dateAndIssueOptions, $supplementNo, $group, $categoriesResultCount)
    {
        $this->period = $period;
        $this->searchKeyword = $searchKeyword;
        $this->yearAndVolume = $yearAndVolume;
        $this->yearAndVolumeOptions = $yearAndVolumeOptions;
        $this->dateAndIssue = $dateAndIssue;
        $this->dateAndIssueOptions = $dateAndIssueOptions;
        $this->supplementNo = $supplementNo;
        $this->group = $group;
        $this->categoriesResultCount = $categoriesResultCount;
    }

    public function updated($property)
    {
        $this->dispatch('update-property', propertyName: 'period', value: $this->period);
    }

    public function checkCategoryIsNotSelect(array $cate): bool
    {
        return empty(array_intersect($cate, $this->category));
    }

    public function checkYearIsInvalid(): bool
    {
        return empty($this->yearAndVolume);
    }

    public function resetFilters(): void
    {
        $this->period = SearchGazettePeriodModel::getDisplayPeriod()[0];
        $this->dispatch('reset-filters');
    }

    // public function submitFilterData(): bool
    public function submitFilterData()
    {
        $this->dispatch('reset-pagination');
        $referer = request()->header('Referer');
        // edit url query
        $parsedUrl = parse_url($referer);
        parse_str($parsedUrl['query'], $queryParams);
        $queryParams['p'] = $this->period;
        $queryParams['yv'] = $this->yearAndVolume;
        $queryParams['di'] = $this->dateAndIssue;
        $queryParams['sn'] = $this->supplementNo;
        $queryParams['g'] = $this->group;
        $queryParams['c'] = implode(',', $this->category);
        $queryParams['kw'] = $this->searchKeyword;
        $queryParams['page'] = 1;
        $modifiedQuery = http_build_query($queryParams);

        $modifiedReferer = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . (isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '') . $parsedUrl['path'] . '?' . $modifiedQuery;

        return redirect($modifiedReferer);
//        return redirect(request()->header('Referer'));
        // return true;
    }

    public function render()
    {
        return view('livewire.search.search-gazette-filter');
    }
}
