<?php

namespace App\Livewire\Search;

use Livewire\Component;
use App\Models\SearchGazettePeriod as SearchGazettePeriodModel;

class SearchGazettePage extends Component
{
    // public string $period = '2024-2020';
    public array $allDisplayPeriod = [];
    public string $period = '';

    public function mount()
    {
        $this->allDisplayPeriod = SearchGazettePeriodModel::getDisplayPeriod();
        $this->period = $this->allDisplayPeriod[0];
    }

    public function doToResultPage(string $cate)
    {
        return redirect()->route('search-gazette-result', ['locale' => app()->getLocale(), 'p' => $this->period, 'c' => $cate]);
    }

    public function render()
    {
        return view('livewire.search.search-gazette-page');
    }
}
