<?php

namespace App\Livewire;

use Livewire\Component;

class BackToTop extends Component
{
    public bool $show_button = false;

    protected $listeners = ['toogleButton'];

    public function toogleButton($status): void
    {
        $this->show_button = $status;
    }

    public function scrollToTop(): void
    {
        $this->dispatch('scrollToTop');
    }

    public function render()
    {
        return view('livewire.back-to-top');
    }
}
