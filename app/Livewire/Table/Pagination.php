<?php

namespace App\Livewire\Table;

use Livewire\Component;
use Livewire\Attributes\Reactive;

class Pagination extends Component
{
    #[Reactive] public $paginationData;
    #[Reactive] public $page;
    public $totalPage;
    public $prevDisabled = false;
    public $nextDisabled = false;
    public $fromCount;
    public $toCount;

    public $currentPage;

    public function mount($paginationData, $page) {
        $this->paginationData = $paginationData;
        $this->totalPage = ceil($this->paginationData['total'] / $this->paginationData['per_page']);
        $this->currentPage = $page;
        $this->page = $page;

        $this->fromCount = ($this->paginationData['current_page'] - 1) * $this->paginationData['per_page'] + 1;
        $this->toCount = $this->paginationData['current_page'] * $this->paginationData['per_page'];
        if ($this->toCount > $this->paginationData['total']) {
            $this->toCount = $this->paginationData['total'];
        }
    }

    public function render()
    {
        return view('livewire.table.pagination');
    }

    public function previousPage() {
        $this->prevDisabled = true;
        if ($this->paginationData['current_page'] > 1) {
            $this->currentPage = $this->paginationData['current_page'] - 1;
            $this->dispatch('previous-page', $this->paginationData['current_page'] - 1);
        }
    }

    public function nextPage() {
        $this->nextDisabled = true;
        if ($this->paginationData['current_page'] < $this->totalPage) {
            $this->currentPage = $this->paginationData['current_page'] + 1;
            $this->dispatch('next-page', $this->paginationData['current_page'] + 1);
        }
    }

    public function updatedcurrentPage($value) {
        $this->prevDisabled = false;
        $this->nextDisabled = false;
        if ((int)$value < 1) {
          $value = 1;
      } else if ((int)$value > $this->totalPage) {
          $value = $this->totalPage;
      }
      $this->currentPage = $value;
      $this->dispatch('change-page', $value);
    }
}
