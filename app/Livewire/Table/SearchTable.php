<?php

namespace App\Livewire\Table;

use Livewire\Attributes\Reactive;
use Livewire\Component;

class SearchTable extends Component
{
    #[Reactive] public $notices;
    #[Reactive] public $page;
    #[Reactive] public $paginationData;
    #[Reactive] public bool $pagination = true;
    #[Reactive] public array $column = [];
    public bool $isShow = true;
    public bool $expandable = false;
    public string $title = '';
    public bool $isLs6 = false;
    public string $id = '';

    public function mount($notices, $paginationData, $expandable = false, $pagination = true, $title = '', $page = 1, $column = [], $isLs6 = false, $id = '')
    {
        $this->expandable = $expandable;
        $this->title = $title;
        $this->pagination = $pagination;
        $this->paginationData = $paginationData;
        $this->notices = $notices;
        $this->page = $page;
        $this->column = $column;
        $this->isLs6 = $isLs6;
        $this->id = $id;
    }

    public function render()
    {
        return view('livewire.table.search-table');
    }
}
