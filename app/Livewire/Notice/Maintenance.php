<?php

namespace App\Livewire\Notice;

use App\Models\WebsiteContent;
use Livewire\Component;

class Maintenance extends Component
{
    public array $data = [];

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'Others')
            ->get()
            ->pluck($content, 'key');

        $classes = [
            '<h1>' => '<h1 class="gld-white-box-title subtitle1">',
            '<p>'  => '<p class="gld-white-box-text body1">',
        ];

        $maintenanceContent = $contents['maintenance_content'] ?? '';

        $this->data['maintenanceNotice'] = addClasses($maintenanceContent, $classes);

        $this->data['maintenanceTitle'] = $contents['maintenance_title'] ?? '';

        return view('livewire.notice.maintenance');
    }
}
