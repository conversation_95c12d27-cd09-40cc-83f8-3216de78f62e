<?php

namespace App\Livewire\Notice;

use App\Models\WebsiteContent;
use Livewire\Component;
use Ryan<PERSON><PERSON><PERSON>\LaravelCloudflareTurnstile\Rules\Turnstile;
use Illuminate\Validation\Rule;

class ImportantNotice extends Component
{
    public bool $showCaptcha = false;

    public array $data = [];

    public string $acceptNotice = '';
    public string $turnstileResponse  = '';

    public function mount($showCaptcha = false)
    {
        $this->showCaptcha = $showCaptcha;
    }


    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'ImportantNotices')
            ->get()
            ->pluck($content, 'key');

        $classes = [
            '<span>' => '<span  style="color: var(--primary-600)" class="body1-text-link">',
            '<p>' => '<p class="gld-card-article-section-body body1">',
            '<li>' => '<li class="body1">',
        ];

        if ($contents->isNotEmpty()) {
            foreach ($contents as $key => $item) {
                if (preg_match('/section_(\d+)_/', $key, $matches)) {
                    $sectionNumber = $matches[1];
                    $this->data[$sectionNumber][$key] = addClasses(html_entity_decode($contents[$key], ENT_QUOTES, "UTF-8"), $classes);
                } else  {
                    $this->data[$key] = html_entity_decode($contents[$key], ENT_QUOTES, "UTF-8");

                }
            }
        }

        return view('livewire.notice.important-notice');
    }

    public function store() {}
}
