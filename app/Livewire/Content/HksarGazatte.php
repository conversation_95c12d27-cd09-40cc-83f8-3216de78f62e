<?php

namespace App\Livewire\Content;

use App\Models\WebsiteContent;
use Livewire\Component;

class HksarGazatte extends Component
{
    public array $data = [];

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'ImportantNotices')
            ->get()
            ->pluck($content, 'key');

        $importantNoticesGazette['title'] = $contents['section_4_title'] ?? '';
        $importantNoticesGazette['content'] = $contents['section_4_content'] ?? '';

        $this->data['content'] = addClasses($importantNoticesGazette['content'], [
            '<span>' => '<span  style="color: var(--primary-600)" class="body1-text-link">',
        ]);

        $this->data['title'] = $importantNoticesGazette['title'];

        return view('livewire.content.hksar-gazatte');
    }
}
