<?php

namespace App\Livewire;

use App\Models\WebsiteContent;
use Livewire\Component;
use Illuminate\Support\Facades\Cookie;

class AcceptCookiePopup extends Component
{
    public bool $show = true;
    public array $data = [];

    public function mount()
    {
        $this->show = !Cookie::get('cookie_popup');
    }

    public function acceptCookies()
    {
        Cookie::queue('cookie_consent', '1', 60 * 24 * 365);
        Cookie::queue('cookie_popup', '1', 60 * 24 * 365);
        $this->show = false;
    }

    public function rejectCookies()
    {
        Cookie::queue('cookie_consent', '0', 60 * 24 * 365);
        Cookie::queue('cookie_popup', '1', 60 * 24 * 365);
        $this->show = false;
    }

    public function render()
    {
        $locale = app()->getLocale();
        $content = $locale . '_content';

        $contents = WebsiteContent::where('page_name', 'Others')
            ->get()
            ->pluck($content, 'key');

        $this->data['cookie'] = html_entity_decode($contents['cookie_text']  ?? '');

        return view('livewire.accept-cookie-popup');
    }
}
