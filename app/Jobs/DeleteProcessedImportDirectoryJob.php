<?php

namespace App\Jobs;

use DirectoryIterator;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class DeleteProcessedImportDirectoryJob implements ShouldQueue
{
    public $timeout = 3600;

    use InteractsWithQueue, Queueable, SerializesModels;

    protected $directory;

    /**
     * Create a new job instance.
     */
    public function __construct($directory)
    {
        $this->directory = $directory;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("" . __METHOD__ . " - Deleting directory: {$this->directory}");
        // delete the directory
        $directoryPath = storage_path('app/private/import/Processing/' . $this->directory);
        if (is_dir($directoryPath)) {
            $files = new DirectoryIterator($directoryPath);
            foreach ($files as $file) {
                if ($file->isDot()) continue;
                if ($file->isFile()) {
                    Log::info("Deleting file: " . $file->getPathname());
                    unlink($file->getPathname());
                } else {
                    rmdir($file->getPathname());
                }
            }
            rmdir($directoryPath);
        }
    }
}
