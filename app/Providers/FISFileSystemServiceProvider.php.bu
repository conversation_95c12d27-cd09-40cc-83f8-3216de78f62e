<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\Filesystem;
use League\Flysystem\WebDAV\WebDAVAdapter;
use Sabre\DAV\Client;

class FISFileSystemServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
        Storage::extend('webdav', function ($app, $config) {
            $client = new Client([
                'baseUri' => $config['webdav_url'],
                'userName' => $config['webdav_user'],
                'password' => $config['webdav_password'],
            ]);

            $adapter = new WebDAVAdapter($client, $config['webdav_path'] ?? '/');

            return new FilesystemAdapter(
                new Filesystem($adapter, $config),
                $adapter,
                $config
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
