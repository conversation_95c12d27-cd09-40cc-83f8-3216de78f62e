<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\Filesystem;
use League\Flysystem\WebDAV\WebDAVAdapter;
use Sabre\DAV\Client;

class FISFileSystemServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
        Storage::extend('webdav', function ($app, $config) {
            $client = new Client([
                'baseUri' => $config['baseUri'],
                'userName' => $config['userName'],
                'password' => $config['password'],
                'authType' => Client::AUTH_BASIC,
                'encoding' => Client::ENCODING_IDENTITY,
                // SSL/Certificate options
                // 'curlSettings' => [
                //     CURLOPT_CAINFO => '/etc/ssl/certs/ega/current/egaat1.ega.gld.hksarg.crt',
                //     CURLOPT_SSL_VERIFYPEER => false,  // Skip SSL peer verification
                //     CURLOPT_SSL_VERIFYHOST => false,  // Skip SSL host verification
                //     CURLOPT_TIMEOUT => 30,            // Connection timeout
                //     CURLOPT_CONNECTTIMEOUT => 10,     // Connection timeout
                // ],
            ]);

            // disable SSL peer and host verification
            $client->addCurlSetting(CURLOPT_SSL_VERIFYPEER, false);
            $client->addCurlSetting(CURLOPT_SSL_VERIFYHOST, false);

            $adapter = new WebDAVAdapter($client, $config['pathPrefix'] ?? '/');

            return new FilesystemAdapter(
                new Filesystem($adapter, $config),
                $adapter,
                $config
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
