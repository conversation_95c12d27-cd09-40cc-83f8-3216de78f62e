<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Elastic\Elasticsearch\ClientBuilder;
use Spatie\PdfToText\Pdf;

class ElasticSearch extends Model
{
    static $SKIP_LIST = ['cgn201923345217.pdf', 'egn201923345217.pdf', 'cgn201721254091.pdf', 'egn201721254091.pdf'];

    public static function createClient()
    {
        return ClientBuilder::create()
            ->setHosts([config('elasticsearch.ES_HOSTS')])
            ->setBasicAuthentication(config('elasticsearch.ES_USERNAME'), config('elasticsearch.ES_PASSWORD'))
            ->build();
    }

    public static function openTheIndex()
    {
        $client = self::createClient();

        $params = [
            'index' => config('elasticsearch.ES_INDEX')
        ];
        
        $response = $client->indices()->open($params);
        Log::info("openTheIndex: " . $response);
    }

    public static function closeTheIndex()
    {
        $client = self::createClient();

        $params = [
            'index' => config('elasticsearch.ES_INDEX')
        ];
        
        $response = $client->indices()->close($params);
        Log::info("closeTheIndex: " . $response);
    }

    public static function createSnapShot()
    {
        $client = self::createClient();

        $snapshotName = 'gazette_snapshot_' . time() . '_' . rand(0, 1000);
        $params = [
            'repository' => config('elasticsearch.ES_SNAPSHOT_REPO'),
            'snapshot' => $snapshotName,
            'body' => [
                'indices' => config('elasticsearch.ES_INDEX'),
                'ignore_unavailable' => true,
                'include_global_state' => false,
            ],
        ];

        $response = null; // Initialize the variable to avoid undefined variable errors

        try {
            $response = $client->snapshot()->create($params);
        } catch (\Exception $e) {
            Log::error("Failed to create snapshot: " . $e->getMessage());
            $response = [
                'error' => true,
                'message' => 'Failed to create snapshot: ' . $snapshotName,
            ];
        }
        Log::info("clientSnapShot: " . json_encode($response));
    }

    public static function restoreFromSnapShot()
    {
        $snapshotName = self::getTheLatestSnapshot();

        if ($snapshotName) {
            $client = self::createClient();

            // close the index before restore
            self::closeTheIndex();
            $params = [
                'repository' => config('elasticsearch.ES_SNAPSHOT_REPO'),
                'snapshot' => $snapshotName,
                'body' => [
                    'indices' => config('elasticsearch.ES_INDEX'),
                    'ignore_unavailable' => true,
                    'include_global_state' => false,
                ],
            ];

            $response = $client->snapshot()->restore($params);
            Log::info($response);
            // open the index after restore
            self::openTheIndex();
        } else {
            Log::error('No snapshot found to restore.');
        }
    }

    public static function getTheLatestSnapshot()
    {
        $result = null;

        $client = self::createClient();

        $params = [
            'repository' => config('elasticsearch.ES_SNAPSHOT_REPO'),
            'snapshot' => '_all',
        ];

        $response = $client->snapshot()->get($params);
        Log::info("getTheLatestSnapshot: " . $response);
        
        $snapshots = $response['snapshots'];
        if ($snapshots) {
          usort($snapshots, function($a, $b) {
              return strtotime($b['end_time']) - strtotime($a['end_time']);
          });

          $result = $snapshots[0]['snapshot'];
        }

        return $result;
    }

    public static function indexDocumentsByGazette($gazette)
    {
        Log::info("indexDocumentsByGazette started at: " . date('Y-m-d H:i:s'));

        $documents = [];
        $mg = Mg::where('gazette_id', $gazette->id)->get();
        $ls1 = Ls1::where('gazette_id', $gazette->id)->get();
        $ls2 = Ls2::where('gazette_id', $gazette->id)->get();
        $ls3 = Ls3::where('gazette_id', $gazette->id)->get();
        $ls4 = Ls4::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();
        $ls5 = Ls5::where('gazette_id', $gazette->id)->get();
        $ls6 = Ls6::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();

        foreach ($mg as $notice) {
            $fileNames = self::getFileNames('mg', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'mg', $notice, $notice->e_title, 'en');
            $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'mg', $notice, $notice->c_title, 'zh');
        }

        foreach ($ls1 as $notice) {
            $fileNames = self::getFileNames('ls1', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls1', $notice, $notice->e_title, 'en');
            $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'ls1', $notice, $notice->c_title, 'zh');
        }

        foreach ($ls2 as $notice) {
            $fileNames = self::getFileNames('ls2', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls2', $notice, $notice->e_title, 'en');
            $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'ls2', $notice, $notice->c_title, 'zh');
        }

        foreach ($ls3 as $notice) {
            $fileNames = self::getFileNames('ls3', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls3', $notice, $notice->e_title, 'en');
            $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'ls3', $notice, $notice->c_title, 'zh');
        }

        foreach ($ls4 as $notice) {
            $fileNames = self::getFileNames('ls4', $gazette, $notice);
            if (!empty($fileNames['eFileName'])) $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls4', $notice, $notice->e_level_title, 'en');
            if (!empty($fileNames['cFileName'])) $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'ls4', $notice, $notice->c_level_title, 'zh');
        }

        foreach ($ls5 as $notice) {
            $fileNames = self::getFileNames('ls5', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls5', $notice, $notice->e_title, 'en');
            $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, 'ls5', $notice, $notice->c_title, 'zh');
        }

        foreach ($ls6 as $notice) {
            $fileNames = self::getFileNames('ls6', $gazette, $notice);
            $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, 'ls6', $notice, $notice->e_title, 'en');
        }

        Log::info($gazette->dateno);
        Log::info($gazette->year.$gazette->volume.$gazette->gno);
        Log::info(count($documents));

        if (count($documents) > 0) {
            self::indexDocuments($documents);
        }

        Log::info("indexDocumentsByGazettes ended at: " . date('Y-m-d H:i:s'));
    }

    public static function getFileNames($type, $gazette, $notice)
    {
        $fileNames = '';
        $eFileName = '';
        $cFileName = '';
        $gno = $gazette->gno;
        if ($gno < 10) $gno = "0".$gazette->gno;
        switch ($type) {
            case 'mg':
                $eFileName = 'egn'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cgn'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls1':
                $eFileName = 'es1'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs1'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls2':
                $eFileName = 'es2'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs2'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls3':
                $eFileName = 'es3'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs3'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls4':
                $eFileName = $notice->e_filename;
                $cFileName = $notice->c_filename;
                break;
            case 'ls5':
                $eFileName = 'es5'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                $cFileName = 'cs5'.$gazette->year.$gazette->volume.$gno.$notice->notice_no.".pdf";
                break;
            case 'ls6':
                $eFileName = 's6'.$gazette->year.$gazette->volume.$gno.'p'.$notice->group.'.pdf';
                break;
        }

        $fileNames = [
            'eFileName' => $eFileName,
            'cFileName' => $cFileName,
        ];

        return $fileNames;
    }

    public static function prepareDocument($filename, $gazette, $notice_type, $notice, $title, $language)
    {
        $gno = $gazette->gno;
        if ($gno < 10) $gno = "0".$gazette->gno;
        $extra = "";
        if ($gazette->extra == "1") $extra = "e";
        // $filePath = storage_path('app/public/egazette/' . $gazette->year.$gazette->volume.$gno.$extra . '/' . $filename);
        $filePath = storage_path('app/private/import/Processing/' . $gazette->year.$gazette->volume.$gno.$extra . '/' . $filename); //TODO: put this link into the .env file
        Log::info("filePath: " . $filePath);
        Log::info("filename; " . $filename);
        Log::info("notice type: " . $notice_type);
        Log::info("gazette id: " . $gazette->id);
        if (file_exists($filePath)) {
            $fileContent = (new Pdf())
              ->setPdf($filePath)->text();

            $result = [
                'index' => 'gazette',
                'body' => [
                    'attachment' => [
                        'content' => $fileContent
                    ],
                    'filename' => $filename,
                    'title' => $title,
                    'language' => $language,
                    'extra' => $gazette->extra,
                    'gazette_id' => $gazette->id,
                    'dateno' => $gazette->dateno,
                    'volume' => $gazette->volume,
                    'gno' => $gazette->gno,
                    'notice_no' => $notice->notice_no ?? '',
                    'notice_type' => $notice_type,
                    'notice_id' => $notice->id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
            ];
            // \Log::info($result);
            return $result;
        }

        return null;
    }

    public static function indexDocuments($documents)
    {
        $client = self::createClient();

        // index the document one by one
        foreach ($documents as $document) {
            if ($document && count($document) > 0 && (!in_array($document['body']['filename'], self::$SKIP_LIST))) {
                $fileContent = $document['body']['attachment']['content'];

                // split the base64 encoded file content into chunks to avoid the error "Request Entity Too Large"
                $chunkSize = config('elasticsearch.ES_CHUNK_SIZE') * 1024 * 1024; // 1MB
                $chunks = str_split($fileContent, $chunkSize);
                foreach ($chunks as $chunk) {
                    $document['data'] = $chunk;
                    self::indexDocumentChnuk($client, $document);
                }
            } else {
                Log::info("Document is empty or skipped: " . json_encode($document));
            }
        }
    }

    public static function indexDocumentChnuk($client, $params, $retries = 3)
    {
        $attempt = 0;
        $delay = config('elastic.ES_RETRY_TIME'); // Initial delay in seconds

        while ($attempt < $retries) {
            try {
                Log::info("indexing document: " . $params['body']['filename']);
                $response = $client->index($params);
                Log::info($response);
                sleep($delay);
                return;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                $attempt++;
                //sleep($delay);
                $delay *= 2; // Exponential backoff
            }
        }

        Log::error('Failed to index documents after ' . $retries . ' attempts.');
    }

    public static function deleteDocumentsByGazetteIdAndNoticeType($gazette, $noticeType)
    {
        $client = self::createClient();

        $params = [
            'index' => config('elasticsearch.ES_INDEX'),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['match' => ['gazette_id' => $gazette->id]],
                            ['match' => ['notice_type' => $noticeType]]
                        ]
                    ]
                ]
            ]
        ];

        try {
            $response = $client->deleteByQuery($params);
            Log::info("deleteDocumentsByGazetteIdAndNoticeType: " . json_encode($response));
        } catch (\Exception $e) {
            Log::error("Error deleting documents: " . $e->getMessage());
        }
    }

    public static function indexDocumentsByGazetteAndNoticeType($gazette, $noticeType)
    {
        Log::info("indexDocumentsByGazette started at: " . date('Y-m-d H:i:s'));
        
        $documents = [];
        if ($noticeType == 'mg') {
            $notices = Mg::where('gazette_id', $gazette->id)->get();
        } else if ($noticeType == 'ls1') {
            $notices = Ls1::where('gazette_id', $gazette->id)->get();
        } else if ($noticeType == 'ls2') {
            $notices = Ls2::where('gazette_id', $gazette->id)->get();
        } else if ($noticeType == 'ls3') {
            $notices = Ls3::where('gazette_id', $gazette->id)->get();
        } else if ($noticeType == 'ls4') {
            $notices = Ls4::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();
        } else if ($noticeType == 'ls5') {
            $notices = Ls5::where('gazette_id', $gazette->id)->get();
        } else if ($noticeType == 'ls6') {
            $notices = Ls6::where('dateno', $gazette->dateno)->where('volume', $gazette->volume)->where('gno', $gazette->gno)->get();
        }

        if ($noticeType == 'ls4') {
            foreach ($notices as $notice) {
                $fileNames = self::getFileNames($noticeType, $gazette, $notice);
                if (!empty($fileNames['eFileName'])) $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, $noticeType, $notice, $notice->e_level_title, 'en');
                if (!empty($fileNames['cFileName'])) $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, $noticeType, $notice, $notice->c_level_title, 'zh');
            }
        } else if ($noticeType == 'ls6') {
            foreach ($notices as $notice) {
                $fileNames = self::getFileNames($noticeType, $gazette, $notice);
                $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, $noticeType, $notice, $notice->e_title, 'en');
            }
        } else {
            foreach ($notices as $notice) {
                $fileNames = self::getFileNames($noticeType, $gazette, $notice);
                $documents[] = self::prepareDocument($fileNames['eFileName'], $gazette, $noticeType, $notice, $notice->e_title, 'en');
                $documents[] = self::prepareDocument($fileNames['cFileName'], $gazette, $noticeType, $notice, $notice->c_title, 'zh');
            }
        }

        if (count($documents) > 0) {
            self::indexDocuments($documents);
        }

        Log::info("indexDocumentsByGazetteAndNoticeType ended at: " . date('Y-m-d H:i:s'));
    }
}
