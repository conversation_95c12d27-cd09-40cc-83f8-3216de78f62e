<?php

namespace App\Models;
use Illuminate\Database\Eloquent\SoftDeletes;

use Illuminate\Database\Eloquent\Model;

class Ls5 extends Model
{
    use SoftDeletes;

    protected $table = 'ls5';
    protected $primaryKey = 'id';

    public function department()
    {
        return $this->belongsTo(Department::class, 'dept_id');
    }

    public function typeDetail()
    {
        return $this->belongsTo(Type::class, 'type');
    }
}
