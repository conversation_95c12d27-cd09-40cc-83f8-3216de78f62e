<?php

namespace App\Models;

use DirectoryIterator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

use App\Jobs\ProcessImportFileJob;
use App\Jobs\DeleteProcessedImportDirectoryJob;

// this model is to be used to handle imports from the publication system.
class Import extends Model
{
    public function __invoke() {
        Log::info("Import invoked");
        // 0 (for Sunday) through 6 (for Saturday)
        $dayOfWeekNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        $weekOfDay = date('w');
        $timeNow = date('H:i:s');
        // Log::info("weekOfDay: " . $dayOfWeekNames[$weekOfDay]);
        
        if ($weekOfDay == 'Monday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Monday';
        } else if ($weekOfDay == 'Tuesday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Tuesday';
        } else if ($weekOfDay == 'Wednesday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Wednesday';
        } else if ($weekOfDay == 'Thursday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Thursday';
        } else if ($weekOfDay == 'Friday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Friday';
        } else if ($weekOfDay == 'Saturday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Saturday';
        } else if ($weekOfDay == 'Sunday' && ($timeNow >= '08:00:00' && $timeNow <= '08:00:05')) {
            $sourceDirectory = 'Sunday';
        } else {
            // extra case
            $sourceDirectory = 'Extra';
        }

        $processingDirectory = 'app/private/import/Processing/';

        // try {
        //     $files = new DirectoryIterator($directory);
        //     $processingDirectory = $baseDirectory . 'Processing/';
        //     foreach ($files as $file) {
        //         Log::info($file->getFileName());
        //         if ($file->getFileName() == '.' || $file->getFileName() == '..') continue;
        //         if ($file->isFile() && $file->getExtension() == 'gaz') {
        //             if (!is_dir($processingDirectory)) {
        //                 mkdir($processingDirectory, 0775, true);
        //             }

        //             rename($directory . $file->getFilename(), $processingDirectory . $file->getFilename());
        //             ProcessImportFileJob::dispatch($processingDirectory . $file->getFilename());
        //         } else {
        //             if (!is_dir($processingDirectory)) {
        //                 mkdir($processingDirectory, 0775, true);
        //             }
                    
        //             rename($directory . $file->getFilename(), $processingDirectory . $file->getFilename());
        //         }
        //     }
        // } catch (\Exception $e) {
        //     Log::error("Error processing import: " . $e->getMessage());
        // }

        try {
            // connect to vpn
            Log::info("start connecting to vpn");
            exec('/usr/local/bin/fis_vpn_connect.sh');
            Log::info("end connecting to vpn");

            $fisDirectories = Storage::disk('fis')->directories(); // Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday, Extra

            if (count($fisDirectories) > 0) {
                foreach ($fisDirectories as $fisDirectory) {
                    if ($fisDirectory == $sourceDirectory) {
                        $sourceDirectories = Storage::disk('fis')->directories($fisDirectory);
                        foreach ($sourceDirectories as $directory) { // Extra/20242815
                            Log::info("Processing directory: " . $directory);
                            // create the same directory in the local storage without the parent folder
                            $newDirectory = str_replace($fisDirectory . '/', '', $directory);
                            if (!is_dir(storage_path($processingDirectory . $newDirectory))) {
                                mkdir(storage_path($processingDirectory . $newDirectory), 0775, true);
                            }
                            $files = Storage::disk('fis')->files($directory);

                            // download the each file into the local storage
                            if (count($files) > 0) {
                                Log::info("download file started at: ".date('Y-m-d H:i:s'));
                                foreach ($files as $file) {
                                    Log::info("Downloading file: " . $file);
                                    $content = Storage::disk('fis')->get($file);
                                    // create the same file in the local storage without the parent folder
                                    $file = str_replace($fisDirectory . '/', '', $file); // Extra/20242815/egn202428151910.pdf -> 20242815/egn202428151910.pdf
                                    file_put_contents(storage_path($processingDirectory . $file), $content);
                                }
                                Log::info("download file ended at: ".date('Y-m-d H:i:s'));

                                $allChecksumCorrect = false;
                                // check the file checksum one by one, making sure the files are downloaded correctly
                                Log::info("check file checksum started at: ".date('Y-m-d H:i:s'));
                                foreach ($files as $file) {
                                    $localFileName = str_replace($fisDirectory . '/', '', $file);
                                    $localFile = storage_path($processingDirectory . $localFileName);
                                    $remoteFile = $file;
                                    $localChecksum = md5_file($localFile);
                                    $remoteChecksum = md5(Storage::disk('fis')->get($remoteFile));
                                    if ($localChecksum !== $remoteChecksum) {
                                        Log::info("File checksum mismatch: " . $file);
                                        $allChecksumCorrect = false;
                                        break;
                                    }
                                }
                                Log::info("check file checksum ended at: ".date('Y-m-d H:i:s'));
                                $allChecksumCorrect = true;

                                if ($allChecksumCorrect) {
                                    // // delete all remote files
                                    // foreach ($files as $file) {
                                    //     Storage::disk('fis')->delete($file);
                                    // }

                                    // // then delete the remote directory
                                    // Storage::disk('fis')->deleteDirectory($directory);

                                    // change the newly created folder and files user and group to nginx
                                    chown(storage_path($processingDirectory . $newDirectory), 'nginx');
                                    chgrp(storage_path($processingDirectory . $newDirectory), 'nginx');
                                    foreach ($files as $file) {
                                        $file = str_replace($fisDirectory . '/', '', $file);
                                        chown(storage_path($processingDirectory . $file), 'nginx');
                                        chgrp(storage_path($processingDirectory . $file), 'nginx');
                                    }

                                    // process the files
                                    $processingDirectory = storage_path($processingDirectory . $newDirectory);
                                    $files = new DirectoryIterator($processingDirectory);

                                    foreach ($files as $file) {
                                        if ($file->getFileName() == '.' || $file->getFileName() == '..') continue;
                                        if ($file->isFile() && $file->getExtension() == 'gaz') {
                                            ProcessImportFileJob::dispatch($processingDirectory . '/' . $file->getFilename());
                                        }
                                    }
                                    // @TODO: create a job to upload files to object storage
                                    DeleteProcessedImportDirectoryJob::dispatch($newDirectory);
                                } else {
                                    Log::info("File checksum mismatch. Aborting.");
                                }
                            }
                        }
                    }
                }
            }

            // disconnect the vpn
            exec('forticlient vpn disconnect');
            Log::info("vpn disconnected");
        } catch (\Exception $e) {
            Log::error("Error processing import: " . $e->getMessage());
        }
    }
}
