<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use DirectoryIterator;

use App\Jobs\ProcessImportFileJob;

// this model is to be used to handle imports from the publication system.
class Import extends Model
{
    public function __invoke() {
        Log::info("Import invoked");
        // 0 (for Sunday) through 6 (for Saturday)
        $dayOfWeekNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        $weekOfDay = date('w');
        $timeNow = date('H:i:s');
        // Log::info("weekOfDay: " . $dayOfWeekNames[$weekOfDay]);

        $baseDirectory = storage_path('app/private/import/');

        if ($weekOfDay == 'Monday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Monday/';
        } else if ($weekOfDay == 'Tuesday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Tuesday/';
        } else if ($weekOfDay == 'Wednesday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Wednesday/';
        } else if ($weekOfDay == 'Thursday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Thursday/';
        } else if ($weekOfDay == 'Friday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Friday/';
        } else if ($weekOfDay == 'Saturday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Saturday/';
        } else if ($weekOfDay == 'Sunday' && $timeNow == '08:00:00') {
            $directory = $baseDirectory . 'Sunday/';
        } else {
            // extra case
            $directory = $baseDirectory . 'Extra/';
        }

        try {
            $files = new DirectoryIterator($directory);
            $processingDirectory = $baseDirectory . 'Processing/';
            foreach ($files as $file) {
                Log::info($file->getFileName());
                if ($file->getFileName() == '.' || $file->getFileName() == '..') continue;
                if ($file->isFile() && $file->getExtension() == 'gaz') {
                    if (!is_dir($processingDirectory)) {
                        mkdir($processingDirectory, 0775, true);
                    }

                    rename($directory . $file->getFilename(), $processingDirectory . $file->getFilename());
                    ProcessImportFileJob::dispatch($processingDirectory . $file->getFilename());
                } else {
                    if (!is_dir($processingDirectory)) {
                        mkdir($processingDirectory, 0775, true);
                    }
                    
                    rename($directory . $file->getFilename(), $processingDirectory . $file->getFilename());
                }
            }
        } catch (\Exception $e) {
            Log::error("Error processing import: " . $e->getMessage());
        }
    }
}
