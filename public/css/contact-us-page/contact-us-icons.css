.address-icon::after {
  mask: url("/images/contact-us/svg/distance.svg") no-repeat;
  mask-size: cover;
}

.email-icon::after {
  mask: url("/images/contact-us/svg/mail.svg") no-repeat;
  mask-size: cover;
}

.fax-icon::after {
  mask: url("/images/contact-us/svg/fax.svg") no-repeat;
  mask-size: cover;
}

.tel-icon::after {
  mask: url("/images/contact-us/svg/call.svg") no-repeat;
  mask-size: cover;
}

.go-to-icon::after {
  mask: url("/images/contact-us/svg/open_in_new.svg") no-repeat;
  mask-size: cover;
}

.bookstore-icon::after {
  mask: url("/images/whats-new/icin.svg") no-repeat;
  mask-size: cover;
}

.gld-svg-container {
  min-width: 18px;
  min-height: 18px;
  width: 18px;
  height: 18px;
  color: inherit;
}

.contact-us-list-subtitle {
  margin-left: calc(18px + var(--spacing-x-sm));
}

.contact-us-list-item a {
    text-decoration: none !important;
    color: var(--primary-600);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-x-sm);
    transition: color 0.3s ease;
}

.contact-us-list-item a:hover {
    color: var(--primary-800);
    transition: color 0.3s ease;
}

@media (min-width: 992px) {
  .gld-svg-container {
    min-width: 24px;
    min-height: 24px;
    width: 24px;
    height: 24px;
  }

  .contact-us-list-subtitle {
    margin-left: calc(24px + var(--spacing-x-sm));
  }
}
