.search-gazette-period-container {
    margin-top: var(--spacing-x-sm);
    background: var(--grey-white);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-base);
    padding: var(--spacing-xx-sm);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 8px;
    row-gap: var(--spacing-xx-sm);
    align-items: center;
}

.search-gazette-period-button{
    position: relative;
    text-align: center;
    height: 40px;
}

.search-gazette-period-button label {
    color: var(--grey-600);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: var(--radius-sm);
    cursor: pointer;
}

.search-gazette-period-button label:hover {
    background: var(--primary-50);
}

.search-gazette-period-button input {
    position: absolute;
    width: 0;
    height: 0;
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
}

.search-gazette-period-button input:checked + label {
    background: var(--primary-600);
    color: var(--grey-white);
}

.schedule-icon::after {
    mask: url("/images/icon/schedule.svg") no-repeat;
    mask-size: cover;
}

@media (min-width: 768px) {
    .search-gazette-period-container {
        margin-top: var(--spacing-md);
        padding: var(--spacing-x-sm);
        grid-template-columns: repeat(4, 1fr) ;
        column-gap: var(--spacing-md);
    }

    .search-gazette-period-button {
        height: 48px;
    }

}

@media print {
    /*.search-gazette-period-button input:checked + label {*/
    /*    background: var(--grey-50);*/
    /*}*/
}
