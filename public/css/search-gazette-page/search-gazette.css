@import url("./search-bar.css");
@import url("./search-period.css");

#search-gazette .gld-main-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-big);
}


#search-gazette-category h2,
#search-gazette-period h2 {
    color: var(--grey-600);
}

#search-gazette-category {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-sm);
}

.search-gazette-category-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-sm);
}

.search-gazette-category-list .search-gazette-category-category-box {
    cursor: pointer;
    text-decoration: none;
    width: 100%;
    background: var(--grey-white);
    box-shadow: var(--shadow-base);
    border-radius: var(--radius-sm);
    padding: var(--spacing-x-big) var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    text-align: center;
}

.search-gazette-category-list .search-gazette-category-category-box:hover {
    text-decoration: none;
}

.search-gazette-category-list .search-gazette-category-category-box p {
    color: var(--grey-600);
}

.search-gazette-category-list .search-gazette-category-category-box .gld-img-container {
    width: 80px;
    height: 80px;
}

.search-gazette-category-list .search-gazette-category-category-box .category-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-x-sm);
}

@media (min-width: 768px) {
    #search-gazette-category {
        gap: var(--spacing-md);
    }

    .search-gazette-category-list .search-gazette-category-category-box {
        padding: var(--spacing-x-big) var(--spacing-lg);
        flex-direction: row;
        justify-content: start;
        gap: var(--spacing-lg);
        text-align: start;
        transition: transform 0.3s ease-in-out;
    }

    .search-gazette-category-list .search-gazette-category-category-box:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
        transition: transform 0.3s ease-in-out;
    }

    .search-gazette-category-list .search-gazette-category-category-box:hover h3 {
        color: var(--primary-600);
    }

    .search-gazette-category-list .search-gazette-category-category-box .gld-img-container {
        width: 110px;
        height: 110px;
    }

    .search-gazette-category-list .search-gazette-category-category-box .category-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xx-sm);
    }
}
