@import url("reset.css");
@import url("fonts.css");
@import url("root.css");
@import url("header.css");
@import url("footer.css");

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: Poppins, sans-serif;
    color: var(--grey-900);
}

html {
    font-size: 16px;
    color: var(--grey-900);
    overflow-y: auto;
    scroll-behavior: smooth;
}

main {
    position: relative;
}

img {
    width: 100%;
    height: 100%;
}

button {
    cursor: pointer;
}

a {
    width: fit-content;
}

a span {
    color: inherit;
}

.svg {
    display: inline-block;
    width: 100%;
    color: currentColor;
    height: 100%;
}

.svg::after {
    display: inherit;
    content: "";
    mask-size: cover;
    width: inherit;
    height: inherit;
    background-color: currentColor;
}

.gld-main-container {
  position: relative;
  padding: var(--spacing-big);
    padding-bottom: var(--spacing-xxx-big);
  width: 100%;
}

.italic {
  font-style: italic;
}

.font-weight-700 {
  font-weight: 700 !important;
}

.font-weight-600 {
  font-weight: 600 !important;
}

.font-weight-500 {
  font-weight: 500 !important;
}

.overlay {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  /*pointer-events: none;*/
}

.skip-to-content-container {
    position: absolute;
    left: -10000px;
    top: 0;
    width: 1px;
    height: 1px;
    overflow: hidden;
    z-index: 100;
}

.skip-to-content-container a {
    border-radius: var(--radius-sm);
    color: var(--primary-600);
    display: block;
    padding: var(--spacing-md);
    background-color: var(--grey-white);
    width: fit-content;
}

.skip-to-content-container:focus-within {
    left: 0;
    width: fit-content;
    height: 100%;
}

.gld-button {
    border: none;
    display: flex;
    align-items: center;
    width: fit-content;
    padding: 0 24px;
    height: 48px;
    background: var(--primary-600);
    color: var(--grey-white);
    border-radius: var(--radius-x-sm);
    text-decoration: none !important;
    transition: 0.2s background-color ease-in-out;
}

.gld-button:hover {
    background: var(--primary-800);
    transition: 0.2s background-color ease;
}

.gld-outline-button {
    display: flex;
    align-items: center;
    width: fit-content;
    padding: 0 24px;
    height: 48px;
    background: var(--grey-white);
    border-radius: var(--radius-x-sm);
    border: 1px solid var(--primary-600);
    color: var(--primary-600);
    transition: 0.2s background-color ease-in-out;
    text-decoration: none !important;
}

.gld-outline-button:hover {
    background: var(--primary-800);
    color: var(--grey-white);
    transition: 0.2s background-color ease;
}

.gld-dashed-button {
    background: none;
    border: none;
    border-bottom: 1px dashed var(--primary-600);
    color: var(--primary-600);
    transition: 0.2s all ease;
    padding: 0 var(--spacing-md);
}

.gld-dashed-button:hover {
    color: var(--primary-800);
    border-bottom: 1px dashed var(--primary-800);
    transition: 0.2s color ease;
}

.gld-dashed-divider {
  border: 1px dashed var(--grey-200);
}

#back-to-top-button {
    position: fixed;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

#back-to-top-button:focus .back-to-top-text {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease;
}

#back-to-top-button .back-to-top-text {
    position: absolute;
    opacity: 0;
    visibility: hidden;
    bottom: -25px;
    color: var(--primary-800);
    text-wrap: nowrap;
    transition: opacity 0.2s ease;
}

#back-to-top-button .gld-svg-container {
    display: inline-block;
    width: 20px;
    height: 20px;
    color: var(--grey-white);
}

#back-to-top-button .arrow-up-icon::after {
    display: inline-block;
    content: "";
    mask: url("/images/icon/arrow_up.svg") no-repeat;
    mask-size: cover;
    width: 100%;
    height: 100%;
    background-color: currentColor;
    transition: transform 0.3s ease;
}

#back-to-top-button:hover .arrow-up-icon:after {
    transform: translateY(-4px);
    transition: transform 0.3s ease;
}

#back-to-top-button:hover .back-to-top-text  {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease;
}

#back-to-top-button.active {
    position: fixed;
    right: 30px;
    z-index: 10;
    width: 40px;
    height: 40px;
    bottom: 40px;
    display: flex;
    opacity: 1;
    pointer-events: unset;
    background: var(--primary-600);
    color: var(--grey-white);
    border-radius: var(--radius-full);
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.2s ease;
    box-shadow: var(--shadow-lg);
    border: none;
}

#back-to-top-button.fixed {
    position: absolute;
}

#back-to-top-button:hover {
    background: var(--primary-800);
    transition: 0.2s background-color ease;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0;
}

.loading-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.65);
    z-index: 9999;
}

.loading-screen .loading-content {
    display: flex;
    height: 100vh;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
}

.loading-screen .loading-text {
    color: var(--grey-white);
}

.loading-screen .loader {
    color: var(--warning-600);
    font-size: 7px;
    width: 1em;
    height: 1em;
    border-radius: 50%;
    position: relative;
    text-indent: -9999em;
    animation: mulShdSpin 1.3s infinite linear;
    transform: translateZ(0);
}

@keyframes mulShdSpin {
    0%,
    100% {
        box-shadow: 0 -3em 0 0.2em,
        2em -2em 0 0em, 3em 0 0 -1em,
        2em 2em 0 -1em, 0 3em 0 -1em,
        -2em 2em 0 -1em, -3em 0 0 -1em,
        -2em -2em 0 0;
    }
    12.5% {
        box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em,
        3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em,
        -2em 2em 0 -1em, -3em 0 0 -1em,
        -2em -2em 0 -1em;
    }
    25% {
        box-shadow: 0 -3em 0 -0.5em,
        2em -2em 0 0, 3em 0 0 0.2em,
        2em 2em 0 0, 0 3em 0 -1em,
        -2em 2em 0 -1em, -3em 0 0 -1em,
        -2em -2em 0 -1em;
    }
    37.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em,
        3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em,
        -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    50% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em,
        3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em,
        -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    62.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em,
        3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0,
        -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
    }
    75% {
        box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em,
        3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em,
        -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
    }
    87.5% {
        box-shadow: 0em -3em 0 0, 2em -2em 0 -1em,
        3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em,
        -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
    }
}


@media (min-width: 992px) {
    .gld-main-container {
        margin: auto;
        max-width: 1440px;
    }

    .gld-main-container {
        padding: var(--spacing-lg) var(--spacing-container);
    }

    .gld-button,
    .gld-outline-button {
        height: 56px;
    }

    .gld-dashed-button {
        padding: 0 4px;
    }
}

@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    header {
        position: relative;
    }

    nav {
        display: none;
    }

    .gld-desktop-menu {
        display: none;
    }
    /** {*/
    /*    background-image: none !important;*/
    /*    color: var(--grey-900) !important;*/
    /*    box-shadow: none !important;*/
    /*    text-shadow: none !important;*/
    /*}*/

    /*.gld-main-container {*/
    /*    padding: var(--spacing-sm);*/
    /*    filter: grayscale(100%);*/
    /*}*/

    /*.gld-main-container,*/
    /*.pages-body {*/
    /*    background: var(--grey-white) !important;*/
    /*}*/

    /*header,*/
    /*button,*/
    /*.gld-pages-banner-container,*/
    /*#back-to-top-button,*/
    /*.circle-arrow-right-button,*/
    /*.gld-highlight-title::before,*/
    /*.svg,*/
    /*.no-print,*/
    /*.gld-button,*/
    /*.gld-outline-button,*/
    /*.gld-dashed-button,*/
    /*footer {*/
    /*    display: none !important;*/
    /*}*/

    /*body {*/
    /*    line-height: 1.5 !important;*/
    /*}*/

}

.top {
    --offset: 50px;

    position: absolute;
    bottom: 40px;
    margin-right:10px;
    margin-top: calc(100vh + var(--offset));

    /*!* visual styling *!*/
    /*text-decoration: none;*/
    /*padding: 10px;*/
    /*font-family: sans-serif;*/
    /*color: #fff;*/
    /*background: #000;*/
    /*border-radius: 100px;*/
    /*white-space: nowrap;*/
}

