.gld-default-table-container {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    border-radius: var(--radius-x-sm);
    box-shadow: var(--shadow-base);
}

.gld-default-table {
    width: 100%;
    height: 100%;
}

.gld-default-table thead tr {
    height: 56px;
    background: var(--primary-50);
}

.gld-default-table tbody {
    background: var(--grey-white);
    tr:not(:last-child) {
        border-bottom: 1px solid var(--grey-200);
    }
}

.gld-default-table th {
    text-align: start;
    vertical-align: middle !important;
    padding: 0 var(--spacing-md) !important;
}

.gld-default-table td {
    padding: var(--spacing-md) !important;
    color: var(--grey-600);
    vertical-align: middle !important;
}


.table-empty-result {
    padding: var(--spacing-xxx-big) var(--spacing-md);
    background: var(--grey-white);
    font-style: italic;
    display: flex;
    justify-content: center;
    color: var(--grey-400);
}


.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-x-sm) 0;
    margin-top: var(--spacing-big);
}

.table-pagination .left {
    display: none;
}

.table-pagination .right {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.table-pagination .right input {
    width: 85px;
    margin: 0 var(--spacing-x-sm);
    padding: 0 var(--spacing-md) 0 16px;
    text-align: center;
    color: var(--grey-900);
}

.table-pagination .gld-svg-container {
    display: block;
    width: 18px;
    height: 18px;
    color: inherit;
}

.table-pagination .table-next-btn,
.table-pagination .table-prev-btn {
    background: var(--grey-white);
    width: 40px;
    height: 40px;
    border-radius: 4px;
    border: none;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    transition: all 0.2s ease;
}

.table-pagination .table-next-btn:disabled,
.table-pagination .table-prev-btn:disabled {
    pointer-events: none;
    background: var(--grey-50);
    color: var(--grey-400);
}

.table-pagination .table-next-btn:hover,
.table-pagination .table-prev-btn:hover {
    background: var(--primary-600);
    color: var(--grey-white);
}

.table-pagination .prev-btn-icon::after {
    display: inline-block;
    content: "";
    mask: url("/images/table/arrow_back.svg") no-repeat;
    mask-size: cover;
    width: 100%;
    height: 100%;
    background-color: currentColor;
}

.table-pagination .next-btn-icon::after {
    display: inline-block;
    content: "";
    mask: url("/images/table/arrow_forward.svg") no-repeat;
    mask-size: cover;
    width: 100%;
    height: 100%;
    background-color: currentColor;
}

.table-pagination #select-table-row-options .select-dropdown-menu {
    width: max-content;
}

.table-select-page {
    display: flex;
    align-items: center;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: var(--spacing-md);
}

@media (min-width: 768px) {
    .table-pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-x-sm) 0;
        margin-top: var(--spacing-big);
    }

    .table-pagination .left {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .table-pagination .table-per-page {
        width: max-content;
    }

    .table-pagination .right {
        justify-content: end;
    }
}

@media (min-width: 992px) {
    .gld-default-table th {
        padding: 0 var(--spacing-big) !important;
    }

    .gld-default-table td {
        padding: var(--spacing-x-big) var(--spacing-big) !important;
    }
}

@media print {
    .gld-default-table-container {
        overflow: visible !important;
    }

    /*.gld-default-table thead tr {*/
    /*    background: var(--grey-50) !important;*/
    /*}*/


    .gld-default-table tbody td{
       padding: var(--spacing-sm) !important;

    }

    .gld-default-table .gld-default-table-header {
        min-width: fit-content !important;
        max-width: fit-content !important;
        padding: var(--spacing-sm) !important;
        width: fit-content !important;
    }
}
