@import url("../default.css");

.toggle-btn {
    cursor: pointer;
}

.gld-pages-banner-container {
    height: 140px;
    width: 100%;
    background: url("/images/banner-image.png") no-repeat bottom;
    background-size: cover;
}

.gld-breadcrumbs {
    background-color: var(--grey-white);
}

.gld-breadcrumbs .gld-breadcrumbs-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xx-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-big);
}

.gld-breadcrumbs ul {
    display: flex;
    align-items: center;
    gap: var(--spacing-x-sm);
}

.gld-breadcrumbs ul a {
    display: inline;
    text-decoration: none;
    color: var(--grey-900);
}

.gld-breadcrumbs ul a:hover {
    text-decoration: underline;
}

.gld-breadcrumbs ul .gld-breadcrumbs-items {
    display: flex;
    gap: var(--spacing-x-sm);
    align-items: center;
}

.gld-breadcrumbs ul .gld-breadcrumbs-items:not(:last-child):after,
.gld-breadcrumbs ul .gld-breadcrumbs-items:first-child:after {
      display: inline-block;
      width: 20px;
      height: 20px;
      min-width: 20px;
      min-height: 20px;
      content: url("/images/breadcrumbs/svg/navigate_next.svg");
}

.gld-breadcrumbs-title {
    color: var(--primary-800);
}

.expand-container {
    transition: all 0.5s ease-in-out;
    overflow: hidden;
}

.expand-container.open {
    transition: all 0.5s ease-in-out;
}

.expand-container.option-open {
    overflow: visible;
}

.expand-container.hide {
    max-height: 0;
    border-width: 0;
    transition: all 0.5s ease-in-out;
}

#the-gov-hksar  ul {
    padding-left: var(--spacing-x-big);
    list-style: disc;
}

#the-gov-hksar a {
    word-break: break-word;
}

#the-gov-hksar ol {
    padding-left: var(--spacing-x-big);
}

#the-gov-hksar ol li {
    font-weight: 700;
}

.pages-body {
  background: var(--grey-50);
  display: flex;
  justify-content: center;
  flex-direction: column;
}

@media (min-width: 992px) {
  .gld-pages-banner-container {
    height: 300px;
  }

  .gld-breadcrumbs .gld-breadcrumbs-container {
      max-width: 1440px;
      margin: auto;
      padding: var(--spacing-x-big) var(--spacing-container);
  }
}

@media print {
  /*.gld-breadcrumbs-container {*/
  /*  padding: 0;*/
  /*  padding-left: var(--spacing-sm) !important;*/
  /*}*/
}
